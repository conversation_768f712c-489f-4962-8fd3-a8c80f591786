<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Browse our comprehensive range of high-quality specialty medications. EDEN Pharmaceuticals offers a wide selection of specialized pharmaceutical products.">
    <title>Specialty Medications - EDEN Pharmaceuticals</title>

    <!-- Preload critical resources -->
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">
    <link rel="preconnect" href="https://images.unsplash.com">

    <!-- Optimized CSS -->
    <link rel="stylesheet" href="../css/optimized.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" media="print" onload="this.media='all'">
    <noscript><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"></noscript>

    <!-- Custom page styles -->
    <style>
        /* Banner styles */
        .products-hero {
            background: linear-gradient(rgba(0,0,0,0.7), rgba(0,0,0,0.7)), url('https://images.unsplash.com/photo-1631549916768-4119b4123a21?ixlib=rb-4.0.3&auto=format&fit=crop&w=1600&q=80');
        }

        /* Download section styles */
        .download-section {
            background-color: #f9fbfd;
            padding: 3rem 0;
            text-align: center;
        }

        .download-content {
            max-width: 800px;
            margin: 0 auto;
        }

        .download-content h2 {
            margin-bottom: 1rem;
            color: #2c3e50;
        }

        .download-content p {
            margin-bottom: 2rem;
            color: #555;
        }

        .download-btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.8rem;
            background-color: #0056b3;
            color: white;
            padding: 1rem 2rem;
            border-radius: 4px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .download-btn:hover {
            background-color: #003d7a;
            transform: translateY(-2px);
        }

        .download-btn i {
            font-size: 1.2rem;
        }

        /* Lightbox styles */
        .product-image {
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .product-image::after {
            content: "\f00e";
            font-family: "Font Awesome 5 Free";
            font-weight: 900;
            position: absolute;
            top: 10px;
            right: 10px;
            background-color: rgba(0, 0, 0, 0.5);
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .product-image:hover::after {
            opacity: 1;
        }

        .lightbox {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease, visibility 0.3s ease;
        }

        .lightbox.active {
            opacity: 1;
            visibility: visible;
        }

        .lightbox-content {
            position: relative;
            max-width: 90%;
            max-height: 90%;
        }

        .lightbox-image {
            display: block;
            max-width: 100%;
            max-height: 90vh;
            box-shadow: 0 5px 25px rgba(0, 0, 0, 0.5);
        }

        .lightbox-close {
            position: absolute;
            top: -40px;
            right: 0;
            width: 30px;
            height: 30px;
            background-color: transparent;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .lightbox-caption {
            position: absolute;
            bottom: -40px;
            left: 0;
            width: 100%;
            color: white;
            text-align: center;
            font-size: 16px;
            padding: 10px 0;
        }

        /* Footer Products Slider Styles */
        .footer-products-slider {
            position: relative;
        }

        .products-slider-container {
            display: flex;
            align-items: center;
            gap: 10px;
            position: relative;
        }

        .products-slider {
            flex: 1;
            overflow: hidden;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.05);
            padding: 10px 0;
        }

        .products-slider-track {
            display: flex;
            transition: transform 0.3s ease;
            gap: 15px;
            padding: 0 15px;
        }

        .product-slide {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            min-width: 120px;
            padding: 15px 10px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            color: rgba(255, 255, 255, 0.9);
            text-decoration: none;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .product-slide:hover {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            transform: translateY(-2px);
            border-color: var(--accent-color);
        }

        .product-slide.active {
            background: var(--accent-color);
            color: white;
            border-color: rgba(255, 255, 255, 0.3);
        }

        .product-slide i {
            font-size: 1.5rem;
            margin-bottom: 8px;
            opacity: 0.9;
        }

        .product-slide span {
            font-size: 0.85rem;
            font-weight: 500;
            line-height: 1.2;
        }

        .slider-btn {
            background: rgba(255, 255, 255, 0.1);
            border: none;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            flex-shrink: 0;
        }

        .slider-btn:hover {
            background: var(--accent-color);
            transform: scale(1.1);
        }

        .slider-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .slider-btn i {
            font-size: 1rem;
        }

        /* Responsive styles for products slider */
        @media (max-width: 768px) {
            .products-slider-container {
                gap: 8px;
            }

            .product-slide {
                min-width: 100px;
                padding: 12px 8px;
            }

            .product-slide i {
                font-size: 1.2rem;
                margin-bottom: 6px;
            }

            .product-slide span {
                font-size: 0.75rem;
            }

            .slider-btn {
                width: 35px;
                height: 35px;
            }

            .slider-btn i {
                font-size: 0.9rem;
            }
        }

        @media (max-width: 480px) {
            .footer-products-slider {
                grid-column: 1 / -1;
            }

            .products-slider-container {
                gap: 5px;
            }

            .product-slide {
                min-width: 85px;
                padding: 10px 6px;
            }

            .product-slide i {
                font-size: 1rem;
                margin-bottom: 5px;
            }

            .product-slide span {
                font-size: 0.7rem;
                line-height: 1.1;
            }

            .slider-btn {
                width: 30px;
                height: 30px;
            }

            .slider-btn i {
                font-size: 0.8rem;
            }
        }
    </style>
</head>
<body>
    <div class="wrapper">
        <header>
            <nav class="main-nav">
                <div class="nav-container">
                    <button class="mobile-menu-btn" aria-label="Toggle menu"><i class="fas fa-bars"></i></button>
                    <div class="logo">
                        <img src="../images/Eden logo PNG.png" alt="EDEN Pharmaceuticals" />
                    </div>
                    <ul class="nav-links">
                        <li><a href="../index.html">Home</a></li>
                        <li><a href="../about.html">About</a></li>
                        <li><a href="../plant.html">Our Plant</a></li>
                        <li class="active"><a href="../products.html">Products</a></li>
                        <li><a class="contact-btn" href="../contact.html">Contact Us</a></li>
                    </ul>
                </div>
            </nav>
        </header>

        <main>
            <!-- Hero Banner Section -->
            <section class="products-hero">
                <div class="products-hero-content">
                    <h1>Specialty Medications</h1>
                    <p>High-quality pharmaceutical products for specialized healthcare needs</p>
                </div>
            </section>

            <!-- Product Gallery Section -->
            <section class="products-gallery">
                <div class="container">
                    <div class="section-header">
                        <h2>Pharmaceutical <span class="highlight-text">Products</span></h2>
                        <p class="section-subtitle">Browse our comprehensive range of high-quality specialty medications</p>
                    </div>

                    <div class="product-grid">
                        <!-- Product 1 -->
                        <div class="product-item">
                            <div class="product-card">
                                <div class="product-image">
                                    <img src="https://images.unsplash.com/photo-1631549916768-4119b4123a21?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80"
                                         width="600" height="400" loading="eager" alt="Oncology Medication">
                                </div>
                                <div class="product-content">
                                    <h3>Oncology Therapy</h3>
                                    <p>Advanced Cancer Treatment</p>
                                    <div class="product-specs">
                                        <span><i class="fas fa-pills"></i> Tablets</span>
                                        <span><i class="fas fa-box"></i> 30 units</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Product 2 -->
                        <div class="product-item">
                            <div class="product-card">
                                <div class="product-image">
                                    <img src="https://images.unsplash.com/photo-1584308666744-24d5c474f2ae?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80"
                                         width="600" height="400" loading="eager" alt="Immunosuppressant Medication">
                                </div>
                                <div class="product-content">
                                    <h3>Immunosuppressant</h3>
                                    <p>Transplant Support Therapy</p>
                                    <div class="product-specs">
                                        <span><i class="fas fa-capsules"></i> Capsules</span>
                                        <span><i class="fas fa-box"></i> 20 units</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Product 3 -->
                        <div class="product-item">
                            <div class="product-card">
                                <div class="product-image">
                                    <img src="https://images.unsplash.com/photo-1579154341098-e4e158cc7f55?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80"
                                         width="600" height="400" loading="lazy" alt="Biological Therapy">
                                </div>
                                <div class="product-content">
                                    <h3>Biological Therapy</h3>
                                    <p>Autoimmune Disease Treatment</p>
                                    <div class="product-specs">
                                        <span><i class="fas fa-syringe"></i> Injectable</span>
                                        <span><i class="fas fa-box"></i> 2 units</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Product 4 -->
                        <div class="product-item">
                            <div class="product-card">
                                <div class="product-image">
                                    <img src="https://images.unsplash.com/photo-1587854692152-cbe660dbde88?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80"
                                         width="600" height="400" loading="lazy" alt="Orphan Drug">
                                </div>
                                <div class="product-content">
                                    <h3>Orphan Drug</h3>
                                    <p>Rare Disease Treatment</p>
                                    <div class="product-specs">
                                        <span><i class="fas fa-pills"></i> Tablets</span>
                                        <span><i class="fas fa-box"></i> 15 units</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Product 5 -->
                        <div class="product-item">
                            <div class="product-card">
                                <div class="product-image">
                                    <img src="https://images.unsplash.com/photo-1550572017-edd951b55104?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80"
                                         width="600" height="400" loading="lazy" alt="Hormone Therapy">
                                </div>
                                <div class="product-content">
                                    <h3>Hormone Therapy</h3>
                                    <p>Endocrine System Support</p>
                                    <div class="product-specs">
                                        <span><i class="fas fa-capsules"></i> Capsules</span>
                                        <span><i class="fas fa-box"></i> 28 units</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Product 6 -->
                        <div class="product-item">
                            <div class="product-card">
                                <div class="product-image">
                                    <img src="https://images.unsplash.com/photo-1471864190281-a93a3070b6de?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80"
                                         width="600" height="400" loading="lazy" alt="Gene Therapy">
                                </div>
                                <div class="product-content">
                                    <h3>Gene Therapy</h3>
                                    <p>Advanced Genetic Treatment</p>
                                    <div class="product-specs">
                                        <span><i class="fas fa-syringe"></i> Injectable</span>
                                        <span><i class="fas fa-box"></i> 1 unit</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Download Section -->
            <section class="download-section">
                <div class="container">
                    <div class="download-content">
                        <h2>Product Information</h2>
                        <p>Download our complete product list with detailed specifications and pricing information.</p>
                        <a href="product-list.pdf" class="download-btn" download="EDKEM-Product-List.pdf">
                            <i class="fas fa-download"></i> Download Product List (PDF)
                        </a>
                    </div>
                </div>
            </section>
        </main>

        <!-- Footer -->
        <footer id="contact">
            <div class="footer-content">
                <div class="footer-nav">
                    <div class="footer-col">
                        <h3>Map</h3>
                        <!-- Map will be added here -->
                        <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3672.832211154431!2d72.5360038250273!3d22.993196717424333!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x395e853ed21ede61%3A0xbbafb667025fe1b1!2sSAHAJ%20SOLITAIRE%2C%20Vishala%2C%20Ahmedabad%2C%20Gujarat%20380007!5e0!3m2!1sen!2sin!4v1746958440405!5m2!1sen!2sin" width="300" height="200" style="border:0;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>
                    </div>
                    <div class="footer-col footer-products-slider">
                        <h3>Product Categories</h3>
                        <div class="products-slider-container">
                            <button class="slider-btn slider-prev" aria-label="Previous products">
                                <i class="fas fa-chevron-left"></i>
                            </button>
                            <div class="products-slider">
                                <div class="products-slider-track">
                                    <a href="../product-pages/01.html" class="product-slide">
                                        <i class="fas fa-pills"></i>
                                        <span>Anti-Cold & Anti-Allergic</span>
                                    </a>
                                    <a href="../product-pages/02.html" class="product-slide">
                                        <i class="fas fa-tablets"></i>
                                        <span>Digestive & Antacids</span>
                                    </a>
                                    <a href="../product-pages/03.html" class="product-slide">
                                        <i class="fas fa-capsules"></i>
                                        <span>Antibiotics</span>
                                    </a>
                                    <a href="../product-pages/04.html" class="product-slide">
                                        <i class="fas fa-band-aid"></i>
                                        <span>Anti-inflammatory</span>
                                    </a>
                                    <a href="../product-pages/05.html" class="product-slide">
                                        <i class="fas fa-leaf"></i>
                                        <span>Nutritional Supplements</span>
                                    </a>
                                    <a href="../product-pages/06.html" class="product-slide">
                                        <i class="fas fa-seedling"></i>
                                        <span>Ayurvedic Products</span>
                                    </a>
                                    <a href="../product-pages/07.html" class="product-slide">
                                        <i class="fas fa-syringe"></i>
                                        <span>Injections</span>
                                    </a>
                                    <a href="../product-pages/08.html" class="product-slide">
                                        <i class="fas fa-pump-medical"></i>
                                        <span>External Preparation</span>
                                    </a>
                                    <a href="../product-pages/09.html" class="product-slide active">
                                        <i class="fas fa-plus-circle"></i>
                                        <span>Other</span>
                                    </a>
                                </div>
                            </div>
                            <button class="slider-btn slider-next" aria-label="Next products">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                    </div>
                    <div class="footer-col">
                        <h3>Our Office</h3>
                        <p>301, Abhishree Complex, Opp Star Bazar</p>
                        <p>Satellite Road, Ahmedabad 12345</p>
                        <p>Phone: +91 9824023088</p>
                        <p>Mobile: +91 7016386329</p>
                        <p>Email: <EMAIL></p>
                    </div>
                    <div class="footer-col">
                        <h3>Business Hours</h3>
                        <p>Monday - Saturday: 9:00 AM - 5:00 PM</p>
                        <p>Sunday: Closed</p>
                        <p>We respond to emails within 24 hours during business days.</p>
                    </div>
                </div>
                <div class="copyright">
                    <p>Copyright © 2025 EDEN. All rights reserved.</p>
                    <div class="social-icons">
                        <a href="#" class="social-icon"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="social-icon"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="social-icon"><i class="fab fa-linkedin-in"></i></a>
                        <a href="#" class="social-icon"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
            </div>
        </footer>

        <a href="#" class="back-to-top" aria-label="Back to top">
            <i class="fas fa-arrow-up"></i>
        </a>
    </div>

    <!-- Lightbox Container -->
    <div class="lightbox" id="productLightbox">
        <div class="lightbox-content">
            <button class="lightbox-close" id="lightboxClose"><i class="fas fa-times"></i></button>
            <img src="" alt="" class="lightbox-image" id="lightboxImage">
            <div class="lightbox-caption" id="lightboxCaption"></div>
        </div>
    </div>

    <!-- Optimized JavaScript -->
    <script src="../js/optimized.js" defer></script>

    <!-- Lightbox JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize lightbox functionality
            initLightbox();
            // Initialize products slider
            initProductsSlider();
        });

        function initLightbox() {
            const productImages = document.querySelectorAll('.product-image img');
            const lightbox = document.getElementById('productLightbox');
            const lightboxImage = document.getElementById('lightboxImage');
            const lightboxCaption = document.getElementById('lightboxCaption');
            const lightboxClose = document.getElementById('lightboxClose');

            // Add click event to all product images
            productImages.forEach(image => {
                image.addEventListener('click', function() {
                    // Get the high-resolution image URL (remove size constraints)
                    let fullSizeUrl = this.src.replace(/&w=\d+&q=\d+/, '');

                    // Set the image source and alt text
                    lightboxImage.src = fullSizeUrl;
                    lightboxImage.alt = this.alt;

                    // Set the caption text
                    const productTitle = this.closest('.product-card').querySelector('h3').textContent;
                    const productDesc = this.closest('.product-card').querySelector('p').textContent;
                    lightboxCaption.textContent = `${productTitle} - ${productDesc}`;

                    // Show the lightbox
                    lightbox.classList.add('active');

                    // Prevent scrolling on the body
                    document.body.style.overflow = 'hidden';
                });
            });

            // Close lightbox when clicking the close button
            lightboxClose.addEventListener('click', closeLightbox);

            // Close lightbox when clicking outside the image
            lightbox.addEventListener('click', function(e) {
                if (e.target === lightbox) {
                    closeLightbox();
                }
            });

            // Close lightbox when pressing Escape key
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && lightbox.classList.contains('active')) {
                    closeLightbox();
                }
            });

            function closeLightbox() {
                lightbox.classList.remove('active');

                // Re-enable scrolling on the body
                document.body.style.overflow = '';

                // Clear the image source after transition (for better memory management)
                setTimeout(() => {
                    if (!lightbox.classList.contains('active')) {
                        lightboxImage.src = '';
                    }
                }, 300);
            }
        }

        function initProductsSlider() {
            const sliderTrack = document.querySelector('.products-slider-track');
            const prevBtn = document.querySelector('.slider-prev');
            const nextBtn = document.querySelector('.slider-next');
            const slides = document.querySelectorAll('.product-slide');

            if (!sliderTrack || !prevBtn || !nextBtn || slides.length === 0) return;

            let currentIndex = 0;
            const slideWidth = 135; // 120px min-width + 15px gap
            const visibleSlides = Math.floor(sliderTrack.parentElement.offsetWidth / slideWidth);
            const maxIndex = Math.max(0, slides.length - visibleSlides);

            function updateSlider() {
                const translateX = -currentIndex * slideWidth;
                sliderTrack.style.transform = `translateX(${translateX}px)`;

                // Update button states
                prevBtn.disabled = currentIndex === 0;
                nextBtn.disabled = currentIndex >= maxIndex;
            }

            function slideNext() {
                if (currentIndex < maxIndex) {
                    currentIndex++;
                    updateSlider();
                }
            }

            function slidePrev() {
                if (currentIndex > 0) {
                    currentIndex--;
                    updateSlider();
                }
            }

            // Event listeners
            nextBtn.addEventListener('click', slideNext);
            prevBtn.addEventListener('click', slidePrev);

            // Touch/swipe support
            let startX = 0;
            let isDragging = false;

            sliderTrack.addEventListener('touchstart', function(e) {
                startX = e.touches[0].clientX;
                isDragging = true;
            }, { passive: true });

            sliderTrack.addEventListener('touchmove', function(e) {
                if (!isDragging) return;
                e.preventDefault();
            }, { passive: false });

            sliderTrack.addEventListener('touchend', function(e) {
                if (!isDragging) return;

                const endX = e.changedTouches[0].clientX;
                const diffX = startX - endX;

                if (Math.abs(diffX) > 50) { // Minimum swipe distance
                    if (diffX > 0) {
                        slideNext();
                    } else {
                        slidePrev();
                    }
                }

                isDragging = false;
            }, { passive: true });

            // Keyboard navigation
            document.addEventListener('keydown', function(e) {
                if (e.target.closest('.products-slider-container')) {
                    if (e.key === 'ArrowLeft') {
                        e.preventDefault();
                        slidePrev();
                    } else if (e.key === 'ArrowRight') {
                        e.preventDefault();
                        slideNext();
                    }
                }
            });

            // Auto-resize handling
            window.addEventListener('resize', function() {
                const newVisibleSlides = Math.floor(sliderTrack.parentElement.offsetWidth / slideWidth);
                const newMaxIndex = Math.max(0, slides.length - newVisibleSlides);

                if (currentIndex > newMaxIndex) {
                    currentIndex = newMaxIndex;
                }
                updateSlider();
            });

            // Initial setup
            updateSlider();
        }
    </script>
</body>
</html>
